import boto3
from boto3.dynamodb.types import TypeDeserializer

from .instances_models import Instance


class DBClient:
    """Database client for handling calls to AWS DynamoDB."""

    def __init__(self) -> None:
        self.client = boto3.client("dynamodb")
        self.deserializer = TypeDeserializer()


class ShiftDBClient(DBClient):
    SHIFT_TABLE_NAME = "s2a-shift-service-shift-data"

    def get_instances_in_range(self, pk: str, start: str, end: str) -> list[Instance]:
        response = self.client.query(
            TableName=self.SHIFT_TABLE_NAME,
            IndexName="Instances",
            KeyConditionExpression="PK = :pk and LSI1_SK between :startDate and :endDate",
            ExpressionAttributeValues={
                ":pk": {"S": pk},
                ":startDate": {"N": str(start)},
                ":endDate": {"N": str(end)},
            },
        )
        data = []
        for item in response["Items"]:
            # Deserialize the DynamoDB item
            deserialized_item = {k: self.deserializer.deserialize(v) for k, v in item.items()}

            # Create Instance and convert to dict excluding None values
            instance = Instance(**deserialized_item)
            instance_dict = instance.model_dump(exclude_none=True, by_alias=True)

            # Create a new Instance from the filtered dict
            filtered_instance = Instance(**instance_dict)
            data.append(filtered_instance)

        return data

    def get_qualification_data(account: str, qualification_ids: list[str]) -> dict:
        """Fetch qualification data for the given qualification IDs.

        Args:
            account: Account identifier
            qualification_ids: List of qualification IDs to fetch

        Returns:
            Dictionary mapping qualification IDs to their data
        """
        # This is a placeholder implementation
        # In a real implementation, you would query a database or service
        # to get the qualification data for each ID

        qualification_data = {}
        try:
            # Example implementation - replace with actual data retrieval logic
            for qual_id in qualification_ids:
                # Fetch qualification data for each ID
                # qualification_data[qual_id] = some_db_client.get_qualification(account, qual_id)
                qualification_data[qual_id] = {"id": qual_id, "account": account}
        except Exception as e:
            LOGGER.exception(f"Failed to fetch qualification data: {e}")

        return qualification_data


class EquipmentDBClient(DBClient):
    EQUIPMENT_CACHE_TABLE_NAME = "s2a-shift-service-equipment-equipment-data"

    def line_exists(self, account: str, line_id: str) -> bool:
        response = self.client.get_item(
            TableName=self.EQUIPMENT_CACHE_TABLE_NAME,
            Key={"account": {"S": account}, "equipmentId": {"S": line_id}},
        )
        return "Item" in response
