import boto3
from boto3.dynamodb.types import TypeDeserializer
from botocore.exceptions import ClientError

from .instances_models import Instance
from .qualification_models import QualificationData, QualificationResponse


class DBClient:
    """Database client for handling calls to AWS DynamoDB."""

    def __init__(self) -> None:
        self.client = boto3.client("dynamodb")
        self.deserializer = TypeDeserializer()


class ShiftDBClient(DBClient):
    SHIFT_TABLE_NAME = "s2a-shift-service-shift-data"

    def get_instances_in_range(self, pk: str, start: str, end: str) -> list[Instance]:
        response = self.client.query(
            TableName=self.SHIFT_TABLE_NAME,
            IndexName="Instances",
            KeyConditionExpression="PK = :pk and LSI1_SK between :startDate and :endDate",
            ExpressionAttributeValues={
                ":pk": {"S": pk},
                ":startDate": {"N": str(start)},
                ":endDate": {"N": str(end)},
            },
        )
        data = []
        for item in response["Items"]:
            # Deserialize the DynamoDB item
            deserialized_item = {k: self.deserializer.deserialize(v) for k, v in item.items()}

            # Create Instance and convert to dict excluding None values
            instance = Instance(**deserialized_item)
            instance_dict = instance.model_dump(exclude_none=True, by_alias=True)

            # Create a new Instance from the filtered dict
            filtered_instance = Instance(**instance_dict)
            data.append(filtered_instance)

        return data

    def get_qualifications_data(self, account: str, qualification_ids: list[str]) -> list[QualificationResponse]:
        """Fetch qualification data for the given qualification IDs.

        Args:
            account: Account identifier
            qualification_ids: List of qualification IDs to fetch

        Returns:
            List of QualificationResponse objects containing qualification data
        """
        qualification_responses = []

        for qualification_id in qualification_ids:
            # Construct PK and SK for DynamoDB query
            pk = f"A#{account}"
            sk = f"Q#{qualification_id}"

            try:
                response = self.client.get_item(
                    TableName=self.SHIFT_TABLE_NAME,
                    Key={
                        "PK": {"S": pk},
                        "SK": {"S": sk}
                    }
                )

                if "Item" in response:
                    # Deserialize the DynamoDB item
                    deserialized_item = {k: self.deserializer.deserialize(v) for k, v in response["Item"].items()}

                    # Filter out unwanted fields for the response
                    filtered_item = {
                        k: v for k, v in deserialized_item.items()
                        if k not in ["account", "entityType", "updatedAt"]
                    }

                    # Create QualificationData object
                    qualification_data = QualificationData(**filtered_item)

                    # Create QualificationResponse object
                    qualification_response = QualificationResponse(
                        qualificationId=qualification_id,
                        qualificationData=qualification_data
                    )

                    qualification_responses.append(qualification_response)

            except Exception as e:
                # Log the error but continue processing other qualification IDs
                print(f"Failed to fetch qualification data for {qualification_id}: {e}")
                continue

        return qualification_responses


class EquipmentDBClient(DBClient):
    EQUIPMENT_CACHE_TABLE_NAME = "s2a-shift-service-equipment-equipment-data"

    def line_exists(self, account: str, line_id: str) -> bool:
        response = self.client.get_item(
            TableName=self.EQUIPMENT_CACHE_TABLE_NAME,
            Key={"account": {"S": account}, "equipmentId": {"S": line_id}},
        )
        return "Item" in response
