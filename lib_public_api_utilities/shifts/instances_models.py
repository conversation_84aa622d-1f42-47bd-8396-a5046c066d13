"""This module defines the Message models."""

import logging
import os

from typing import Optional
from pydantic import BaseModel, Field

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(os.getenv("LOG_LEVEL") or logging.INFO)


class Instance(BaseModel):
    """This class defines the Shift instance model."""

    name: str
    instance_class: str = Field(..., alias="class")
    start: int
    end: int
    account: str
    line_id: str = Field(..., alias="lineId")
    instance_id: str = Field(..., alias="instanceId")


class InstanceDB(Instance):
    class Config:
        extra = "allow"  # Allow extra fields
