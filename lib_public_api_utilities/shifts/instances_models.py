"""This module defines the Message models."""

import logging
import os

from pydantic import BaseModel, Field

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(os.getenv("LOG_LEVEL") or logging.INFO)


class Instance(BaseModel):
    """This class defines the Shift instance model."""

    name: str
    instance_class: str = Field(..., alias="class")
    start: int
    end: int
    account: str
    line_id: str = Field(..., alias="lineId")
    instance_id: str = Field(..., alias="instanceId")
    qualification_id: str = Field(default=None, alias="qualificationId")


class InstanceDB(Instance):
    class Config:
        extra = "allow"  # Allow extra fields


class QualificationText(BaseModel):
    """Model for qualification text in different locales."""

    description: str
    locale: str


class QualificationData(BaseModel):
    """Model for qualification data from DynamoDB."""

    model_config = {"populate_by_name": True}

    description: str
    qualification_id: str = Field(..., alias="qualificationId")
    texts: list[QualificationText]


class QualificationResponse(BaseModel):
    """Response model for qualification data."""

    qualification_id: str = Field(..., alias="qualificationId")
    qualification_data: QualificationData = Field(..., alias="qualificationData")
