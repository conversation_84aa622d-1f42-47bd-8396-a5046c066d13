"""Unit tests for db_client module."""

from unittest.mock import Mock, patch
import pytest
from botocore.exceptions import ClientError
from fastapi import status

from lib_public_api_utilities.error_handling.gravitee_error_handler import GraviteeHTTPException
from lib_public_api_utilities.error_handling.http_error_responses import HTTP_500_INTERNAL
from lib_public_api_utilities.shifts.db_client import ShiftD<PERSON>lient
from lib_public_api_utilities.shifts.qualification_models import QualificationData, QualificationResponse


class TestShiftDBClientGetQualificationsData:
    """Test cases for ShiftDBClient.get_qualifications_data method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.db_client = ShiftDBClient()
        self.account = "readykit-replay"
        self.qualification_ids = ["qualification1", "qualification2"]

    def test_get_qualifications_data_success_single_qualification(self):
        """Test successful retrieval of a single qualification."""
        # Arrange
        mock_response = {
            "Items": [
                {
                    "PK": {"S": "A#readykit-replay"},
                    "SK": {"S": "Q#qualification1"},
                    "account": {"S": "readykit-replay"},
                    "description": {"S": "qualification1"},
                    "entityType": {"S": "qualification"},
                    "qualificationId": {"S": "qualification1"},
                    "texts": {
                        "L": [
                            {
                                "M": {
                                    "description": {"S": "qualification1"},
                                    "locale": {"S": "de"}
                                }
                            },
                            {
                                "M": {
                                    "description": {"S": "qualification1"},
                                    "locale": {"S": "en"}
                                }
                            }
                        ]
                    },
                    "updatedAt": {"N": "*************"}
                }
            ]
        }

        with patch.object(self.db_client.client, 'query', return_value=mock_response):
            # Act
            result = self.db_client.get_qualifications_data(self.account, ["qualification1"])

            # Assert
            assert len(result) == 1
            assert isinstance(result[0], QualificationResponse)
            assert result[0].qualification_id == "qualification1"
            assert isinstance(result[0].qualification_data, QualificationData)
            assert result[0].qualification_data.description == "qualification1"
            assert result[0].qualification_data.qualification_id == "qualification1"
            assert len(result[0].qualification_data.texts) == 2

    def test_get_qualifications_data_success_multiple_qualifications(self):
        """Test successful retrieval of multiple qualifications."""
        # Arrange
        mock_responses = [
            {
                "Items": [
                    {
                        "PK": {"S": "A#readykit-replay"},
                        "SK": {"S": "Q#qualification1"},
                        "account": {"S": "readykit-replay"},
                        "description": {"S": "qualification1"},
                        "entityType": {"S": "qualification"},
                        "qualificationId": {"S": "qualification1"},
                        "texts": {"L": []}
                    }
                ]
            },
            {
                "Items": [
                    {
                        "PK": {"S": "A#readykit-replay"},
                        "SK": {"S": "Q#qualification2"},
                        "account": {"S": "readykit-replay"},
                        "description": {"S": "qualification2"},
                        "entityType": {"S": "qualification"},
                        "qualificationId": {"S": "qualification2"},
                        "texts": {"L": []}
                    }
                ]
            }
        ]

        with patch.object(self.db_client.client, 'query', side_effect=mock_responses):
            # Act
            result = self.db_client.get_qualifications_data(self.account, self.qualification_ids)

            # Assert
            assert len(result) == 2
            assert result[0].qualification_id == "qualification1"
            assert result[1].qualification_id == "qualification2"

    def test_get_qualifications_data_no_items_found_raises_exception(self):
        """Test when no qualification items are found - should raise GraviteeHTTPException."""
        # Arrange
        mock_response = {"Items": []}

        with patch.object(self.db_client.client, 'query', return_value=mock_response):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException) as exc_info:
                self.db_client.get_qualifications_data(self.account, ["NON-EXISTENT"])

            assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            assert exc_info.value.message == HTTP_500_INTERNAL

    def test_get_qualifications_data_filters_unwanted_fields(self):
        """Test that unwanted fields are filtered out from the response."""
        # Arrange
        mock_response = {
            "Items": [
                {
                    "PK": {"S": "A#readykit-replay"},
                    "SK": {"S": "Q#qualification1"},
                    "account": {"S": "readykit-replay"},  # Should be filtered
                    "description": {"S": "qualification1"},
                    "entityType": {"S": "qualification"},  # Should be filtered
                    "qualificationId": {"S": "qualification1"},
                    "texts": {"L": []},
                    "updatedAt": {"N": "*************"}  # Should be filtered
                }
            ]
        }

        with patch.object(self.db_client.client, 'query', return_value=mock_response):
            # Act
            result = self.db_client.get_qualifications_data(self.account, ["qualification1"])

            # Assert
            qualification_data = result[0].qualification_data
            # Check that filtered fields are not present in the model
            assert hasattr(qualification_data, 'description')
            assert hasattr(qualification_data, 'qualification_id')
            assert hasattr(qualification_data, 'texts')
            # These fields should not be in the model definition
            assert not hasattr(qualification_data, 'account')
            assert not hasattr(qualification_data, 'entity_type')
            assert not hasattr(qualification_data, 'updated_at')

    def test_get_qualifications_data_client_error_raises_gravitee_exception(self):
        """Test that ClientError raises GraviteeHTTPException."""
        # Arrange
        client_error = ClientError(
            error_response={'Error': {'Code': 'ResourceNotFoundException', 'Message': 'Table not found'}},
            operation_name='Query'
        )

        with patch.object(self.db_client.client, 'query', side_effect=client_error):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException) as exc_info:
                self.db_client.get_qualifications_data(self.account, ["CO-Quali"])

            assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            assert exc_info.value.message == HTTP_500_INTERNAL

    def test_get_qualifications_data_general_exception_raises_gravitee_exception(self):
        """Test that general Exception raises GraviteeHTTPException."""
        # Arrange
        with patch.object(self.db_client.client, 'query', side_effect=Exception("Unexpected error")):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException) as exc_info:
                self.db_client.get_qualifications_data(self.account, ["qualification1"])

            assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            assert exc_info.value.message == HTTP_500_INTERNAL

    def test_get_qualifications_data_correct_dynamodb_query_parameters(self):
        """Test that correct DynamoDB query parameters are used."""
        # Arrange
        mock_response = {"Items": []}

        with patch.object(self.db_client.client, 'query', return_value=mock_response) as mock_query:
            # Act & Assert - Should raise exception due to empty items
            with pytest.raises(GraviteeHTTPException):
                self.db_client.get_qualifications_data(self.account, ["qualification1"])

            # Assert query was called with correct parameters
            mock_query.assert_called_once_with(
                TableName="s2a-shift-service-shift-data",
                KeyConditionExpression="PK = :pk and SK = :sk",
                ExpressionAttributeValues={
                    ":pk": {"S": "A#readykit-replay"},
                    ":sk": {"S": "Q#qualification1"}
                }
            )

    def test_get_qualifications_data_empty_qualification_ids_list(self):
        """Test with empty qualification IDs list."""
        # Act
        result = self.db_client.get_qualifications_data(self.account, [])

        # Assert
        assert len(result) == 0

    @patch('lib_public_api_utilities.shifts.db_client.LOGGER')
    def test_get_qualifications_data_logs_no_items_error(self, mock_logger):
        """Test that no items found is properly logged."""
        # Arrange
        mock_response = {"Items": []}

        with patch.object(self.db_client.client, 'query', return_value=mock_response):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException):
                self.db_client.get_qualifications_data(self.account, ["NON-EXISTENT"])

            # Assert logging was called
            mock_logger.error.assert_called_once_with(
                "No Items key found in DynamoDB response for qualification: %s, account: %s",
                "NON-EXISTENT",
                self.account
            )

    def test_get_qualifications_data_partial_success_with_missing_qualification(self):
        """Test when some qualifications are found but others are missing."""
        # Arrange
        mock_responses = [
            {
                "Items": [
                    {
                        "PK": {"S": "A#readykit-replay"},
                        "SK": {"S": "Q#qualification1"},
                        "description": {"S": "qualification1"},
                        "qualificationId": {"S": "qualification"},
                        "texts": {"L": []}
                    }
                ]
            },
            {
                "Items": []  # Second qualification not found
            }
        ]

        with patch.object(self.db_client.client, 'query', side_effect=mock_responses):
            # Act & Assert - Should raise exception on first missing qualification
            with pytest.raises(GraviteeHTTPException) as exc_info:
                self.db_client.get_qualifications_data(self.account, ["CO-Quali", "NON-EXISTENT"])

            assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            assert exc_info.value.message == HTTP_500_INTERNAL

    @patch('lib_public_api_utilities.shifts.db_client.LOGGER')
    def test_get_qualifications_data_logs_client_error(self, mock_logger):
        """Test that ClientError is properly logged."""
        # Arrange
        client_error = ClientError(
            error_response={'Error': {'Code': 'AccessDenied', 'Message': 'Access denied'}},
            operation_name='Query'
        )

        with patch.object(self.db_client.client, 'query', side_effect=client_error):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException):
                self.db_client.get_qualifications_data(self.account, ["CO-Quali"])

            # Assert logging was called
            mock_logger.exception.assert_called_once_with(
                "An error occurred while trying to fetch qualification data for qualification: %s",
                "CO-Quali"
            )

    @patch('lib_public_api_utilities.shifts.db_client.LOGGER')
    def test_get_qualifications_data_logs_general_exception(self, mock_logger):
        """Test that general Exception is properly logged."""
        # Arrange
        with patch.object(self.db_client.client, 'query', side_effect=Exception("Unexpected error")):
            # Act & Assert
            with pytest.raises(GraviteeHTTPException):
                self.db_client.get_qualifications_data(self.account, ["CO-Quali"])

            # Assert logging was called
            mock_logger.exception.assert_called_once_with(
                "An unexpected error occurred while trying to fetch qualification data for qualification: %s",
                "CO-Quali"
            )
